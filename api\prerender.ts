// Bot user agents that should receive prerendered content
const BOT_USER_AGENTS = [
  'googlebot',
  'bingbot',
  'slurp', // Yahoo
  'duckduckbot',
  'baidus<PERSON>er',
  'yandexbot',
  'facebookexternalhit',
  'twitterbot',
  'linkedinbot',
  'pinterestbot',
  'redditbot',
  'whatsapp',
  'telegram',
  'discord',
  'slack',
  'skype',
  'applebot',
  'developers.google.com/+/web/snippet',
  // Generic HTTP clients and scripts
  'python-requests',
  'requests',
  'aiohttp',
  'go-http-client',
  'curl',
  'wget',
  'python',
  'ruby',
  'java',
  'php',
  'perl',
  'node',
];

// Cache duration for prerendered content (1 hour)
const CACHE_DURATION = 3600;

// Enhanced meta tags for better SEO and social sharing
const META_TAGS = {
  title: 'Econic Media | Luxury Web Design & Professional Product Photography',
  description: 'Transform your brand with luxury web design and professional product photography. Modern, responsive websites and stunning product visuals that convert visitors into customers.',
  keywords: 'luxury web design, product photography, professional photography, modern websites, responsive design, brand transformation, e-commerce photography, web development',
  ogTitle: 'Econic Media | Luxury Web Design & Professional Product Photography',
  ogDescription: 'Transform your brand with luxury web design and professional product photography. Modern, responsive websites and stunning product visuals that convert visitors into customers.',
  ogImage: 'https://www.econicmedia.pro/websitepreview.png',
  ogUrl: 'https://www.econicmedia.pro',
  twitterCard: 'summary_large_image',
  twitterSite: '@EconicMedia',
  twitterTitle: 'Econic Media | Luxury Web Design & Professional Product Photography',
  twitterDescription: 'Transform your brand with luxury web design and professional product photography. Modern, responsive websites and stunning product visuals.',
  twitterImage: 'https://www.econicmedia.pro/websitepreview.png',
};

// Route-specific content for better SEO
const ROUTE_CONTENT = {
  '/': {
    title: 'Econic Media | Luxury Web Design & Professional Product Photography',
    h1: 'Transform Your Brand with Luxury Web Design',
    content: `
      <main>
        <section>
          <h1>Transform Your Brand with Luxury Web Design</h1>
          <p>We create stunning, modern websites and professional product photography that convert visitors into customers. Our luxury design approach ensures your brand stands out in today's competitive market.</p>
        </section>
        
        <section>
          <h2>Our Services</h2>
          <div>
            <h3>Web Design & Development</h3>
            <p>Modern, responsive websites built with cutting-edge technology. From concept to launch, we create digital experiences that engage and convert.</p>
          </div>
          <div>
            <h3>Product Photography</h3>
            <p>Professional product photography that showcases your products in the best light. High-quality images that drive sales and enhance your brand image.</p>
          </div>
          <div>
            <h3>Brand Strategy</h3>
            <p>Comprehensive brand strategy to help your business stand out from the competition. We develop cohesive brand identities that resonate with your target audience.</p>
          </div>
        </section>
        
        <section>
          <h2>Why Choose Econic Media</h2>
          <p>With years of experience in luxury web design and professional photography, we understand what it takes to create compelling digital experiences. Our team combines technical expertise with creative vision to deliver results that exceed expectations.</p>
        </section>
      </main>
    `
  },
  '/services': {
    title: 'Our Services | Econic Media',
    h1: 'Professional Web Design & Photography Services',
    content: `
      <main>
        <h1>Professional Web Design & Photography Services</h1>
        <p>Discover our comprehensive range of services designed to elevate your brand and drive business growth.</p>
      </main>
    `
  },
  '/portfolio': {
    title: 'Portfolio | Econic Media',
    h1: 'Our Work Portfolio',
    content: `
      <main>
        <h1>Our Work Portfolio</h1>
        <p>Explore our collection of luxury websites and professional product photography projects.</p>
      </main>
    `
  },
  '/contact': {
    title: 'Contact Us | Econic Media',
    h1: 'Get In Touch',
    content: `
      <main>
        <h1>Get In Touch</h1>
        <p>Ready to transform your brand? Contact us today to discuss your project and get a custom quote.</p>
      </main>
    `
  }
};

function isBotUserAgent(userAgent: string, acceptHeader?: string): boolean {
  if (!userAgent) return false;
  const ua = userAgent.toLowerCase();

  // Check against known bot patterns
  const isKnownBot = BOT_USER_AGENTS.some(bot => ua.includes(bot));
  if (isKnownBot) return true;

  // Heuristic: If UA does not contain 'Mozilla' and Accept header includes 'text/html', treat as crawler
  const isMozillaBased = ua.includes('mozilla');
  const acceptsHtml = acceptHeader?.includes('text/html') || false;

  if (!isMozillaBased && acceptsHtml) {
    return true;
  }

  return false;
}

function generatePrerenderedHTML(url: string, route: string): string {
  const routeData = ROUTE_CONTENT[route as keyof typeof ROUTE_CONTENT] || ROUTE_CONTENT['/'];
  
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${routeData.title}</title>
  <meta name="description" content="${META_TAGS.description}">
  <meta name="keywords" content="${META_TAGS.keywords}">
  <meta name="author" content="Econic Media">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="${META_TAGS.ogUrl}${route}">
  <meta property="og:title" content="${routeData.title}">
  <meta property="og:description" content="${META_TAGS.ogDescription}">
  <meta property="og:image" content="${META_TAGS.ogImage}">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:image:alt" content="Econic Media - Modern web design and professional product photography for ambitious brands">
  <meta property="og:site_name" content="Econic Media">
  
  <!-- Twitter -->
  <meta property="twitter:card" content="${META_TAGS.twitterCard}">
  <meta property="twitter:url" content="${META_TAGS.ogUrl}${route}">
  <meta property="twitter:title" content="${routeData.title}">
  <meta property="twitter:description" content="${META_TAGS.twitterDescription}">
  <meta property="twitter:image" content="${META_TAGS.twitterImage}">
  <meta property="twitter:image:alt" content="Econic Media - Modern web design and professional product photography for ambitious brands">
  <meta property="twitter:site" content="${META_TAGS.twitterSite}">
  
  <!-- Additional SEO -->
  <link rel="canonical" href="${META_TAGS.ogUrl}${route}">
  <meta name="robots" content="index, follow">
  <meta name="googlebot" content="index, follow">
  
  <!-- Favicon -->
  <link rel="icon" href="/newlogofinal.png" type="image/png">
  
  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Econic Media",
    "url": "https://econicmedia.pro",
    "logo": "https://econicmedia.pro/newlogofinal.png",
    "description": "${META_TAGS.description}",
    "sameAs": [
      "https://twitter.com/EconicMedia"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    }
  }
  </script>
  
  <!-- Prerender indicator -->
  <meta name="prerender" content="true">
</head>
<body>
  ${routeData.content}
  
  <!-- Footer content for SEO -->
  <footer>
    <p>&copy; 2025 Econic Media. All rights reserved.</p>
    <p>Professional web design and product photography services.</p>
  </footer>
</body>
</html>`;
}

export default async function handler(req: Request): Promise<Response> {
  try {
    const userAgent = req.headers.get('user-agent') || '';
    const acceptHeader = req.headers.get('accept') || '';
    const reqUrl = new URL(req.url);
    const url = reqUrl.searchParams.get('url') || '/';

    // Check if this is actually a bot request
    const isBot = isBotUserAgent(userAgent, acceptHeader);

    // Add debug logging
    console.log('Prerender request:', {
      userAgent,
      acceptHeader,
      url,
      isBot,
      timestamp: new Date().toISOString()
    });

    if (!isBot) {
      // This should not happen if Vercel routing is working correctly
      // Non-bots should be routed to /index.html directly
      console.warn('Non-bot request reached prerender function:', { userAgent, acceptHeader, url });

      // Redirect to the main site to let Vercel handle it properly
      return Response.redirect(new URL('/', req.url), 302);
    }
    
    // Clean up the URL path
    const cleanPath = url.startsWith('/') ? url : `/${url}`;
    const route = cleanPath === '/' ? '/' : cleanPath;
    
    // Generate prerendered HTML
    const html = generatePrerenderedHTML(req.url, route);
    
    // Return the prerendered HTML with appropriate headers
    return new Response(html, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': `public, max-age=${CACHE_DURATION}, s-maxage=${CACHE_DURATION}, stale-while-revalidate=300`,
        'X-Prerender': 'true',
        'X-Served-By': 'prerender',
        'X-Robots-Tag': 'index, follow',
        'X-User-Agent-Debug': userAgent,
      },
    });
    
  } catch (error) {
    console.error('Prerender error:', error);
    
    // Fallback to a basic HTML response
    const fallbackHTML = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${META_TAGS.title}</title>
  <meta name="description" content="${META_TAGS.description}">
</head>
<body>
  <h1>Econic Media</h1>
  <p>Luxury web design and professional product photography.</p>
</body>
</html>`;
    
    return new Response(fallbackHTML, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'X-Prerender': 'true',
        'X-Served-By': 'prerender',
        'X-Prerender-Error': 'true',
      },
    });
  }
}
